
<!DOCTYPE html>
<html class="dark">

<head>
  <meta charset="UTF-8">
  <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
  <title>SC Operator KI</title>

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Preline UI -->
  <link rel="stylesheet" href="https://preline.co/assets/css/main.min.css">
  <script src="https://preline.co/assets/js/hs-ui.bundle.js"></script>

  <style>
    /* Background SVG styling */
    body {
      background-image: url('../../GiRa_background.svg');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      background-attachment: fixed;
      min-height: 100vh;
    }

    /* Floating dock animations and glass morphism */
    #floating-dock {
      animation: slideUpFade 0.5s ease-out;
    }

    @keyframes slideUpFade {
      from {
        opacity: 0;
        transform: translate(-50%, 20px);
      }
      to {
        opacity: 1;
        transform: translate(-50%, 0);
      }
    }

    #floating-dock button {
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    #floating-dock button::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: all 0.3s ease;
    }

    #floating-dock button:hover::before {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }

    /* Glass morphism effect for dock container */
    .glass-morphism {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    /* Dock icon styling */
    .dock-icon {
      width: 20px;
      height: 20px;
      stroke-width: 1.5;
      transition: all 0.3s ease;
    }

    #floating-dock button:hover .dock-icon {
      transform: scale(1.1);
      stroke-width: 2;
    }

    /* Navbar glassmorphism styles */
    header.glass-panel {
      background: rgba(31, 41, 55, 0.2);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.05);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      position: sticky;
      top: 0;
      z-index: 40;
    }

    header.glass-panel:hover {
      background: rgba(31, 41, 55, 0.25);
      border-color: rgba(255, 255, 255, 0.08);
      transform: none; /* Prevent navbar movement on hover */
      box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
    }

    /* Navbar button glassmorphism */
    header .glass-card {
      background: rgba(55, 65, 81, 0.2);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.05);
      transition: all 0.3s ease;
    }

    header .glass-card:hover {
      background: rgba(75, 85, 99, 0.3);
      border-color: rgba(255, 255, 255, 0.1);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    /* Logo container styling */
    .logo-container {
      background: rgba(55, 65, 81, 0.15);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.03);
      transition: all 0.3s ease;
    }

    .logo-container:hover {
      background: rgba(75, 85, 99, 0.25);
      border-color: rgba(255, 255, 255, 0.08);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .logo-container img {
      filter: brightness(0.9) contrast(1.1);
      transition: all 0.3s ease;
    }

    .logo-container:hover img {
      filter: brightness(1) contrast(1.2);
      transform: scale(1.05);
    }

    /* AI Assistant Brain Sphere Animation */
    .ai-brain-sphere {
      position: relative;
      width: 160px;
      height: 160px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
    }

    .brain-container {
      position: relative;
      width: 150px;
      height: 150px;
      border-radius: 50%;
      background: radial-gradient(circle at 30% 30%, #7c3aed, #6d28d9, #4c1d95);
      overflow: hidden;
      box-shadow:
        0 0 40px rgba(139, 92, 246, 0.5),
        inset 0 0 40px rgba(168, 85, 247, 0.3);
    }

    .brain-mesh {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        /* Horizontal flowing lines */
        linear-gradient(90deg, transparent 0%, rgba(168, 85, 247, 0.4) 20%, transparent 40%, rgba(192, 132, 252, 0.3) 60%, transparent 80%, rgba(139, 92, 246, 0.5) 100%),
        /* Vertical flowing lines */
        linear-gradient(0deg, transparent 0%, rgba(192, 132, 252, 0.3) 25%, transparent 50%, rgba(168, 85, 247, 0.4) 75%, transparent 100%),
        /* Diagonal mesh pattern */
        linear-gradient(45deg, transparent 30%, rgba(139, 92, 246, 0.2) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(192, 132, 252, 0.2) 50%, transparent 70%);
      background-size:
        100% 30px,
        30px 100%,
        60px 60px,
        60px 60px;
      border-radius: 50%;
      animation: brainFlow 4s ease-in-out infinite;
    }

    .brain-waves {
      position: absolute;
      top: 10%;
      left: 10%;
      right: 10%;
      bottom: 10%;
      background:
        radial-gradient(ellipse at 20% 30%, rgba(168, 85, 247, 0.6) 0%, transparent 50%),
        radial-gradient(ellipse at 80% 70%, rgba(192, 132, 252, 0.4) 0%, transparent 50%),
        radial-gradient(ellipse at 50% 20%, rgba(139, 92, 246, 0.3) 0%, transparent 60%),
        radial-gradient(ellipse at 30% 80%, rgba(168, 85, 247, 0.5) 0%, transparent 40%);
      border-radius: 50%;
      animation: waveFlow 6s ease-in-out infinite reverse;
    }

    .brain-core {
      position: absolute;
      top: 25%;
      left: 25%;
      right: 25%;
      bottom: 25%;
      background: radial-gradient(circle, rgba(192, 132, 252, 0.8) 0%, rgba(139, 92, 246, 0.4) 50%, transparent 100%);
      border-radius: 50%;
      animation: coreGlow 3s ease-in-out infinite;
    }

    .brain-particles {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 50%;
    }

    .particle {
      position: absolute;
      width: 3px;
      height: 3px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      box-shadow: 0 0 6px rgba(168, 85, 247, 0.8);
      animation: particleFloat 3s ease-in-out infinite;
    }

    .particle:nth-child(1) { top: 25%; left: 35%; animation-delay: 0s; }
    .particle:nth-child(2) { top: 35%; right: 30%; animation-delay: 0.5s; }
    .particle:nth-child(3) { bottom: 35%; left: 30%; animation-delay: 1s; }
    .particle:nth-child(4) { bottom: 30%; right: 35%; animation-delay: 1.5s; }
    .particle:nth-child(5) { top: 45%; left: 55%; animation-delay: 2s; }
    .particle:nth-child(6) { top: 55%; right: 45%; animation-delay: 2.5s; }

    @keyframes brainFlow {
      0%, 100% {
        transform: rotate(0deg) scale(1);
        opacity: 0.8;
      }
      25% {
        transform: rotate(90deg) scale(1.05);
        opacity: 0.9;
      }
      50% {
        transform: rotate(180deg) scale(0.95);
        opacity: 1;
      }
      75% {
        transform: rotate(270deg) scale(1.02);
        opacity: 0.9;
      }
    }

    @keyframes waveFlow {
      0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.6;
      }
      33% {
        transform: scale(1.1) rotate(120deg);
        opacity: 0.8;
      }
      66% {
        transform: scale(0.9) rotate(240deg);
        opacity: 0.7;
      }
    }

    @keyframes coreGlow {
      0%, 100% {
        transform: scale(1);
        opacity: 0.6;
      }
      50% {
        transform: scale(1.2);
        opacity: 0.9;
      }
    }

    @keyframes particleFloat {
      0%, 100% {
        opacity: 0.4;
        transform: translateY(0px) scale(0.8);
      }
      50% {
        opacity: 1;
        transform: translateY(-15px) scale(1.4);
      }
    }

    /* Modal Animations */
    .modal-enter {
      opacity: 1;
    }

    .modal-enter #modalContent {
      transform: scale(1);
      opacity: 1;
    }

    .modal-exit {
      opacity: 0;
    }

    .modal-exit #modalContent {
      transform: scale(0.95);
      opacity: 0;
    }

    /* Custom Range Slider */
    .slider::-webkit-slider-thumb {
      appearance: none;
      height: 20px;
      width: 20px;
      border-radius: 50%;
      background: linear-gradient(45deg, #8b5cf6, #6366f1);
      cursor: pointer;
      box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
    }

    .slider::-moz-range-thumb {
      height: 20px;
      width: 20px;
      border-radius: 50%;
      background: linear-gradient(45deg, #8b5cf6, #6366f1);
      cursor: pointer;
      border: none;
      box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
    }

    /* Vertical Stepper Styles */
    .stepper-container {
      position: relative;
    }

    .step {
      display: flex;
      align-items: flex-start;
      margin-bottom: 18px;
      position: relative;
    }

    .step:last-child {
      margin-bottom: 0;
    }

    .step-number {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 14px;
      margin-right: 16px;
      position: relative;
      z-index: 2;
      transition: all 0.3s ease;
    }

    .step-number.active {
      background: linear-gradient(135deg, #8b5cf6, #6366f1);
      color: white;
      box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
    }

    .step-number.completed {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
    }

    .step-number.upcoming {
      background: rgba(107, 114, 128, 0.2);
      color: #9ca3af;
      border: 2px solid #374151;
    }

    .step-content {
      flex: 1;
      min-height: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .step-title {
      font-weight: 600;
      color: #f9fafb;
      margin-bottom: 4px;
    }

    .step-description {
      color: #9ca3af;
      font-size: 14px;
    }

    .step-line {
      position: absolute;
      left: 19px;
      top: 50px;
      width: 2px;
      height: calc(100% - 50px);
      background: #374151;
      z-index: 1;
    }

    .step-line.active {
      background: linear-gradient(180deg, #8b5cf6, #6366f1);
    }

    .step-line.completed {
      background: linear-gradient(180deg, #10b981, #059669);
    }

    .step:last-child .step-line {
      display: none;
    }

    .step-form {
      margin-top: 12px;
      padding: 12px;
      background: rgba(55, 65, 81, 0.3);
      border-radius: 8px;
      border: 1px solid rgba(75, 85, 99, 0.5);
    }

    .step-form.hidden {
      display: none;
    }

    /* Card layout specific styles */
    .workflow-card {
      min-height: 400px;
      max-height: 60vh;
      overflow-y: auto;
    }

    .workflow-card .stepper-container {
      max-height: calc(60vh - 180px);
      overflow-y: auto;
      padding-right: 8px;
    }

    /* AI Assistant card height constraint */
    .ai-assistant-card {
      min-height: 400px;
      max-height: 60vh;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
    }

    /* Custom scrollbar for AI Assistant card */
    .ai-assistant-card::-webkit-scrollbar {
      width: 6px;
    }

    .ai-assistant-card::-webkit-scrollbar-track {
      background: rgba(55, 65, 81, 0.3);
      border-radius: 3px;
    }

    .ai-assistant-card::-webkit-scrollbar-thumb {
      background: rgba(139, 92, 246, 0.5);
      border-radius: 3px;
    }

    .ai-assistant-card::-webkit-scrollbar-thumb:hover {
      background: rgba(139, 92, 246, 0.7);
    }

    /* Custom scrollbar for workflow card */
    .workflow-card .stepper-container::-webkit-scrollbar {
      width: 6px;
    }

    .workflow-card .stepper-container::-webkit-scrollbar-track {
      background: rgba(55, 65, 81, 0.3);
      border-radius: 3px;
    }

    .workflow-card .stepper-container::-webkit-scrollbar-thumb {
      background: rgba(139, 92, 246, 0.5);
      border-radius: 3px;
    }

    .workflow-card .stepper-container::-webkit-scrollbar-thumb:hover {
      background: rgba(139, 92, 246, 0.7);
    }

    /* Responsive adjustments */
    @media (max-width: 1024px) {
      .workflow-card, .ai-assistant-card {
        min-height: auto;
        max-height: none;
      }

      .workflow-card .stepper-container {
        max-height: none;
        overflow-y: visible;
      }
    }


  </style>

</head>

<body class="bg-gray-900 text-white">
  <!-- Header -->
  <header class="glass-panel border-b border-gray-700/50 px-6 py-4 backdrop-blur-xl">
    <div class="flex items-center justify-between">
      <!-- Left Section - Title -->
      <div class="flex items-center space-x-4">
        <div>
          <h1 class="text-2xl font-bold text-white">SC Operator KI</h1>
        </div>
      </div>
      
      <!-- Center Section - Mercedes Logo -->
      <div class="absolute left-1/2 transform -translate-x-1/2">
        <div class="">
          <img src="../assets/media/logo/mb_star.png" alt="Mercedes-Benz" class="h-10 w-10 opacity-90 hover:opacity-100 transition-all duration-300 hover:scale-110">
        </div>
      </div>
      
      <!-- Right Section - Company Logos -->
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-3">
          <!-- MO PSCA Logo -->
          <div class="">
            <img src="../assets/media/logo/mo_psca.png" alt="MO PSCA" class="h-8 w-auto opacity-90 hover:opacity-100 transition-opacity duration-200" onerror="console.log('Failed to load MO PSCA logo:', this.src, window.location.href); this.style.display='none';">
          </div>
          <!-- MO HUB Logo -->
          <div class="">
            <img src="../assets/media/logo/mo_hub.png" alt="MO HUB" class="h-8 w-auto opacity-90 hover:opacity-100 transition-opacity duration-200" onerror="console.log('Failed to load MO HUB logo:', this.src, window.location.href); this.style.display='none';">
          </div>
        </div>
      </div>
    </div>
  </header>

  <div class="max-w-7xl mx-auto p-6">
    <div class="space-y-8">
      <!-- AI Assistant Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Left Card: AI Assistant -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 ai-assistant-card">
          <h3 class="text-lg font-semibold text-white mb-4 text-center">AI Assistant</h3>

          <!-- AI Brain Sphere -->
          <div class="ai-brain-sphere">
            <div class="brain-container">
              <div class="brain-mesh"></div>
              <div class="brain-waves"></div>
              <div class="brain-core"></div>
              <div class="brain-particles">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
              </div>
            </div>
          </div>

          <!-- Start AI Process Button -->
          <div class="flex justify-center mb-4">
            <button id="startAiButton" class="group relative inline-flex h-12 w-12 items-center justify-center overflow-hidden rounded-full font-medium text-white transition-all duration-300 hover:w-32 shadow-lg hover:shadow-teal-500/25 border-2 backdrop-blur-sm" style="background: linear-gradient(rgba(255,255,255,0.1), rgba(255,255,255,0.05)) padding-box, linear-gradient(45deg, #14b8a6, #06b6d4, #8b5cf6, #14b8a6) border-box; border: 2px solid transparent;">
              <div class="inline-flex whitespace-nowrap opacity-0 transition-all duration-200 group-hover:-translate-x-3 group-hover:opacity-100">Start AI</div>
              <div class="absolute right-3.5">
                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5">
                  <path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                </svg>
              </div>
            </button>
          </div>
        </div>

        <!-- Right Card: SC Copilot KI - Autonomous Operation -->
        <div class="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 workflow-card">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-8 h-8 relative">
              <div class="w-full h-full rounded-full bg-gradient-to-br from-purple-500 to-indigo-600 shadow-lg shadow-purple-500/25 animate-pulse"></div>
            </div>
            <h3 class="text-xl font-semibold text-white">SC Copilot KI - Autonomous Operation</h3>
          </div>

          <div class="space-y-4">
            <div class="progress-step" id="progress-step-1">
              <div class="flex items-center space-x-3">
                <div class="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-white text-sm font-medium">1</div>
                <div class="flex-1">
                  <div class="text-gray-300 font-medium">Critical Material Detection</div>
                </div>
                <div class="ml-auto">
                  <div class="w-5 h-5 border-2 border-gray-600 border-t-purple-500 rounded-full animate-spin hidden" id="spinner-1"></div>
                  <div class="w-5 h-5 text-green-500 hidden" id="check-1">✓</div>
                  <div class="w-5 h-5 text-red-500 hidden" id="error-1">✗</div>
                </div>
              </div>
            </div>

            <div class="progress-step" id="progress-step-2">
              <div class="flex items-center space-x-3">
                <div class="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-white text-sm font-medium">2</div>
                <div class="flex-1">
                  <div class="text-gray-300 font-medium">Automated Email Composition & Sending</div>
                  <div class="text-xs text-gray-500 mt-1">Reads stakeholder emails from email_info.csv</div>
                </div>
                <div class="ml-auto">
                  <div class="w-5 h-5 border-2 border-gray-600 border-t-purple-500 rounded-full animate-spin hidden" id="spinner-2"></div>
                  <div class="w-5 h-5 text-green-500 hidden" id="check-2">✓</div>
                  <div class="w-5 h-5 text-red-500 hidden" id="error-2">✗</div>
                </div>
              </div>
            </div>

            <div class="progress-step" id="progress-step-3">
              <div class="flex items-center space-x-3">
                <div class="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-white text-sm font-medium">3</div>
                <div class="flex-1">
                  <div class="text-gray-300 font-medium">AI-Powered Email Analysis</div>
                  <div class="text-xs text-gray-500 mt-1">Detects emails → stores in incoming_emails.json → analyzes with Azure OpenAI</div>
                </div>
                <div class="ml-auto">
                  <div class="w-5 h-5 border-2 border-gray-600 border-t-purple-500 rounded-full animate-spin hidden" id="spinner-3"></div>
                  <div class="w-5 h-5 text-green-500 hidden" id="check-3">✓</div>
                  <div class="w-5 h-5 text-red-500 hidden" id="error-3">✗</div>
                </div>
              </div>
            </div>

            <div class="progress-step" id="progress-step-4">
              <div class="flex items-center space-x-3">
                <div class="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-white text-sm font-medium">4</div>
                <div class="flex-1">
                  <div class="text-gray-300 font-medium">Enhanced Dashboard Update</div>
                </div>
                <div class="ml-auto">
                  <div class="w-5 h-5 border-2 border-gray-600 border-t-purple-500 rounded-full animate-spin hidden" id="spinner-4"></div>
                  <div class="w-5 h-5 text-green-500 hidden" id="check-4">✓</div>
                  <div class="w-5 h-5 text-red-500 hidden" id="error-4">✗</div>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 p-4 bg-gray-700/50 rounded-lg">
            <div class="text-sm text-gray-400 mb-2">Status:</div>
            <div class="text-white" id="progress-status">Ready to start autonomous workflow...</div>
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <button id="progress-close" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors hidden">
              Close
            </button>
            <button id="progress-cancel" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors hidden">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>



  <!-- Floating Dock -->
  <div id="floating-dock" class="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50">
    <div class="glass-morphism rounded-xl px-3 py-2 shadow-2xl">
      <div class="flex items-center space-x-3">
        <!-- Home/Main Page -->
        <button onclick="goToMainPage()" class="group flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700/60 hover:bg-blue-600/80 transition-all duration-200 hover:scale-105 relative overflow-hidden" title="Main Page">
          <svg xmlns="http://www.w3.org/2000/svg" class="dock-icon text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
          </svg>
        </button>
        
        <!-- Cockpit/Dashboard -->
        <button onclick="openCockpit()" class="group flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700/60 hover:bg-purple-600/80 transition-all duration-200 hover:scale-105 relative overflow-hidden" title="Cockpit Dashboard">
          <svg xmlns="http://www.w3.org/2000/svg" class="dock-icon text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z" />
          </svg>
        </button>

        <!-- Email Client -->
        <button onclick="openEmailClient()" class="group flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700/60 hover:bg-gray-600/80 transition-all duration-200 hover:scale-105 relative overflow-hidden" title="Email Client">
          <svg xmlns="http://www.w3.org/2000/svg" class="dock-icon text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <script src="../assets/js/python.js"></script>
  <script src="../assets/js/renderer.js"></script>
  <!-- Removed hero-ui-entry.js script as part of React removal -->

  <script>
    // Progress tracking variables
    let isProcessRunning = false;
    let currentProcessStep = 0;
    const startAiButton = document.getElementById('startAiButton');

    // Event listeners
    startAiButton.addEventListener('click', startAutonomousWorkflow);

    // API Base URL
    const API_BASE_URL = 'http://127.0.0.1:7777';



    // Update progress step
    function updateProgressStep(step, status, message = '') {
      console.log(`🔄 Updating progress step ${step} to ${status}: ${message}`);

      const spinner = document.getElementById(`spinner-${step}`);
      const check = document.getElementById(`check-${step}`);
      const error = document.getElementById(`error-${step}`);
      const statusElement = document.getElementById('progress-status');

      if (!spinner || !check || !error || !statusElement) {
        console.error('❌ Progress elements are missing!', {
          spinner: !!spinner,
          check: !!check,
          error: !!error,
          statusElement: !!statusElement
        });
        return;
      }

      // Hide all indicators first
      spinner.classList.add('hidden');
      check.classList.add('hidden');
      error.classList.add('hidden');

      // Show appropriate indicator
      if (status === 'loading') {
        spinner.classList.remove('hidden');
      } else if (status === 'success') {
        check.classList.remove('hidden');
      } else if (status === 'error') {
        error.classList.remove('hidden');
      }

      // Update status message
      if (message) {
        statusElement.textContent = message;
      }

      console.log(`✅ Progress step ${step} updated successfully`);
    }

    // API call functions
    async function callAPI(endpoint, method = 'POST', data = null) {
      try {
        const url = `${API_BASE_URL}${endpoint}`;
        console.log(`🌐 Making API call: ${method} ${url}`);

        const options = {
          method: method,
          headers: {
            'Content-Type': 'application/json',
          }
        };

        if (data && method !== 'GET') {
          options.body = JSON.stringify(data);
        }

        console.log(`📤 Request options:`, options);

        const response = await fetch(url, options);
        console.log(`📥 Response status: ${response.status} ${response.statusText}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`❌ API Error Response:`, errorText);
          throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }

        const result = await response.json();
        console.log(`✅ API Success:`, result);

        return result;
      } catch (error) {
        console.error(`❌ API call failed for ${endpoint}:`, error);
        throw error;
      }
    }

    // Autonomous workflow function
    async function startAutonomousWorkflow() {
      if (isProcessRunning) {
        console.log('⚠️ Workflow already running, skipping...');
        return;
      }

      console.log('🚀 Starting autonomous SC Copilot KI workflow...');

      // First check API connectivity
      try {
        const connectivityResponse = await fetch(`${API_BASE_URL}/hello/connectivity-test`);
        if (!connectivityResponse.ok) {
          throw new Error(`API server not responding (status: ${connectivityResponse.status})`);
        }
        console.log('✅ API connectivity confirmed');
      } catch (error) {
        console.error('❌ API connectivity check failed:', error);
        showAPIStatus('disconnected', 'Cannot connect to API server');
        alert('❌ Cannot connect to API server!\n\nPlease start the FastAPI server first:\n1. Run: python start_server.py\n2. Wait for server to start\n3. Try again');
        return;
      }

      isProcessRunning = true;
      currentProcessStep = 0;

      // Show cancel button and update status
      const cancelButton = document.getElementById('progress-cancel');
      const closeButton = document.getElementById('progress-close');

      if (cancelButton) {
        cancelButton.classList.remove('hidden');
        cancelButton.onclick = () => {
          console.log('🛑 User cancelled autonomous workflow');
          isProcessRunning = false;
          cancelButton.classList.add('hidden');
          closeButton.classList.remove('hidden');
          document.getElementById('progress-status').textContent = 'Workflow cancelled by user.';
        };
      }

      if (closeButton) {
        closeButton.classList.add('hidden');
      }

      try {
        // Step 1: Check Critical Materials
        currentProcessStep = 1;
        updateProgressStep(1, 'loading', 'Scanning data.csv for RED and YELLOW status materials...');

        const criticalMaterialsResult = await callAPI('/check-critical-materials/');
        updateProgressStep(1, 'success', `Found ${criticalMaterialsResult.total_critical_materials} critical materials - Stored in critical_materials.json`);

        if (!isProcessRunning) return;

        // Step 2: Compose and Send Emails
        currentProcessStep = 2;
        updateProgressStep(2, 'loading', 'Creating custom emails for warehouse, supplier, and logistics stakeholders...');

        try {
          console.log('🔄 Starting Step 2: Compose and Send Emails');
          console.log('📧 Reading stakeholder emails from email_info.csv...');
          const emailResult = await callAPI('/compose-and-send-emails/');
          console.log('📧 Email result:', emailResult);
          updateProgressStep(2, 'success', `Sent ${emailResult.emails_sent_count || emailResult.successful_sends || 0} emails automatically using Outlook integration`);
          console.log('✅ Step 2 completed successfully, proceeding to step 3...');
        } catch (step2Error) {
          console.error('❌ Step 2 failed:', step2Error);
          updateProgressStep(2, 'error', `Step 2 failed: ${step2Error.message}`);
          throw step2Error; // Re-throw to be caught by main try-catch
        }

        if (!isProcessRunning) {
          console.log('⚠️ Process was cancelled after step 2, stopping workflow');
          return;
        }

        // Step 3: Detect and Analyze Email Responses
        currentProcessStep = 3;
        updateProgressStep(3, 'loading', 'Reading stakeholder emails from email_info.csv and monitoring Outlook inbox...');

        try {
          console.log('🔄 Starting Step 3a: Detect Stakeholder Emails');
          console.log('📧 Reading stakeholder definitions from email_info.csv...');
          console.log('📥 Scanning Outlook inbox for emails from defined stakeholders...');

          // First detect emails from stakeholders defined in email_info.csv
          const detectResult = await callAPI('/detect-stakeholder-emails/');
          console.log('📨 Detect result:', detectResult);

          if (detectResult.new_emails_count > 0) {
            console.log(`📧 Found ${detectResult.new_emails_count} new stakeholder emails`);
            console.log('💾 Emails stored in incoming_emails.json');
          } else {
            console.log('📧 No new stakeholder emails found');
          }

          updateProgressStep(3, 'loading', 'Analyzing email responses with Azure OpenAI...');
          console.log('🔄 Starting Step 3b: Analyze Emails with AI');
          console.log('🧠 Using Azure OpenAI to extract structured information...');

          // Then analyze them with AI (this will analyze emails stored in incoming_emails.json)
          const analysisResult = await callAPI('/analyze-emails-with-ai/');
          console.log('🧠 Analysis result:', analysisResult);

          const totalAnalyzed = analysisResult.analyzed_count || analysisResult.total_analyzed || 0;
          updateProgressStep(3, 'success', `Analyzed ${totalAnalyzed} emails - Extracted structured information and categorized by stakeholder type`);
          console.log('✅ Step 3 completed successfully, proceeding to step 4...');
        } catch (step3Error) {
          console.error('❌ Step 3 failed:', step3Error);
          updateProgressStep(3, 'error', `Step 3 failed: ${step3Error.message}`);
          throw step3Error; // Re-throw to be caught by main try-catch
        }

        if (!isProcessRunning) {
          console.log('⚠️ Process was cancelled after step 3, stopping workflow');
          return;
        }

        // Step 4: Update Cockpit Stats
        currentProcessStep = 4;
        updateProgressStep(4, 'loading', 'Updating enhanced dashboard with analysis results...');

        try {
          console.log('🔄 Starting Step 4: Update Cockpit Stats');
          const statsResult = await callAPI('/update-cockpit-stats/', 'GET');
          console.log('📊 Stats result:', statsResult);
          updateProgressStep(4, 'success', 'Dashboard updated with real-time analytics and insights');
          console.log('✅ Step 4 completed successfully!');
        } catch (step4Error) {
          console.error('❌ Step 4 failed:', step4Error);
          updateProgressStep(4, 'error', `Step 4 failed: ${step4Error.message}`);
          throw step4Error; // Re-throw to be caught by main try-catch
        }

        // Final status
        document.getElementById('progress-status').textContent = '✅ Autonomous SC Copilot KI workflow completed successfully!';

        // Show close button and hide cancel
        const closeButton = document.getElementById('progress-close');
        const cancelButton = document.getElementById('progress-cancel');

        if (closeButton) {
          closeButton.classList.remove('hidden');
          closeButton.onclick = () => {
            console.log('✅ Resetting workflow interface');
            isProcessRunning = false;
            closeButton.classList.add('hidden');
            document.getElementById('progress-status').textContent = 'Ready to start autonomous workflow...';

            // Reset all progress indicators
            for (let i = 1; i <= 4; i++) {
              const spinner = document.getElementById(`spinner-${i}`);
              const check = document.getElementById(`check-${i}`);
              const error = document.getElementById(`error-${i}`);
              if (spinner) spinner.classList.add('hidden');
              if (check) check.classList.add('hidden');
              if (error) error.classList.add('hidden');
            }

            // Refresh the cockpit data if user is on cockpit page
            if (typeof loadCSVData === 'function') {
              loadCSVData();
            }
          };
        }

        if (cancelButton) {
          cancelButton.classList.add('hidden');
        }

      } catch (error) {
        console.error('❌ Autonomous workflow failed at step', currentProcessStep, ':', error);
        console.error('❌ Error details:', {
          message: error.message,
          stack: error.stack,
          step: currentProcessStep
        });

        const errorMessage = `Error in step ${currentProcessStep}: ${error.message}`;
        updateProgressStep(currentProcessStep, 'error', errorMessage);

        // Update status with detailed error
        const statusElement = document.getElementById('progress-status');
        if (statusElement) {
          statusElement.textContent = `❌ Workflow failed at step ${currentProcessStep}: ${error.message}`;
        }

        // Show close button and hide cancel
        const closeBtn = document.getElementById('progress-close');
        const cancelBtn = document.getElementById('progress-cancel');

        if (closeBtn) {
          closeBtn.classList.remove('hidden');
          closeBtn.onclick = () => {
            console.log('❌ Resetting workflow interface after error');
            isProcessRunning = false;
            closeBtn.classList.add('hidden');
            document.getElementById('progress-status').textContent = 'Ready to start autonomous workflow...';

            // Reset all progress indicators
            for (let i = 1; i <= 4; i++) {
              const spinner = document.getElementById(`spinner-${i}`);
              const check = document.getElementById(`check-${i}`);
              const error = document.getElementById(`error-${i}`);
              if (spinner) spinner.classList.add('hidden');
              if (check) check.classList.add('hidden');
              if (error) error.classList.add('hidden');
            }
          };
        }

        if (cancelBtn) {
          cancelBtn.classList.add('hidden');
        }
      }
    }



    // Test API connectivity on page load
    async function testAPIConnectivity() {
      try {
        console.log('🔍 Testing API connectivity...');
        const response = await fetch(`${API_BASE_URL}/hello/connectivity-test`);
        if (response.ok) {
          const result = await response.text();
          console.log('✅ API connectivity test successful:', result);
          showAPIStatus('connected');
        } else {
          console.warn('⚠️ API connectivity test failed:', response.status, response.statusText);
          showAPIStatus('error', `Server responded with status ${response.status}`);
        }
      } catch (error) {
        console.error('❌ API connectivity test error:', error);
        console.log('🔧 Make sure the FastAPI server is running on http://127.0.0.1:7777');
        showAPIStatus('disconnected', 'Cannot connect to API server');
      }
    }

    // Show API connection status
    function showAPIStatus(status, message = '') {
      const statusElement = document.getElementById('progress-status');
      if (!statusElement) return;

      if (status === 'connected') {
        statusElement.innerHTML = '✅ API Server Connected - Ready to start autonomous workflow...';
        statusElement.className = 'text-green-400';
      } else if (status === 'disconnected') {
        statusElement.innerHTML = `❌ API Server Not Running<br><small class="text-gray-400">Please start the server: <code>python start_server.py</code></small>`;
        statusElement.className = 'text-red-400';
      } else if (status === 'error') {
        statusElement.innerHTML = `⚠️ API Server Error: ${message}<br><small class="text-gray-400">Check server logs for details</small>`;
        statusElement.className = 'text-yellow-400';
      }
    }

    // Run connectivity test
    testAPIConnectivity();
  </script>
</body>

</html>

