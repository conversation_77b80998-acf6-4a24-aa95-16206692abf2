#!/usr/bin/env python3
"""
Start a simple HTTP server to serve the frontend files
This prevents CORS issues when accessing the API from the frontend
"""

import os
import sys
import http.server
import socketserver
import webbrowser
import time
from pathlib import Path

def start_frontend_server():
    """Start a simple HTTP server for the frontend"""
    
    # Change to the public directory
    public_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'public')
    
    if not os.path.exists(public_dir):
        print(f"❌ Public directory not found: {public_dir}")
        print("   Make sure you're running this from the project root directory")
        return False
    
    os.chdir(public_dir)
    
    # Configuration
    PORT = 8080
    HOST = "127.0.0.1"
    
    print("🌐 Starting Frontend Server for SC Copilot KI")
    print("=" * 50)
    print(f"📁 Serving files from: {public_dir}")
    print(f"🌐 Frontend URL: http://{HOST}:{PORT}")
    print(f"📄 Main page: http://{HOST}:{PORT}/src/index.html")
    print(f"📧 Email page: http://{HOST}:{PORT}/src/email.html")
    print()
    
    try:
        # Create the server
        handler = http.server.SimpleHTTPRequestHandler
        
        # Add CORS headers to prevent issues
        class CORSRequestHandler(handler):
            def end_headers(self):
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', '*')
                super().end_headers()
        
        with socketserver.TCPServer((HOST, PORT), CORSRequestHandler) as httpd:
            print(f"🚀 Server started successfully!")
            print(f"📝 Press Ctrl+C to stop the server")
            print("=" * 50)
            
            # Open the browser automatically
            main_url = f"http://{HOST}:{PORT}/src/index.html"
            print(f"🌐 Opening browser to: {main_url}")
            
            # Wait a moment then open browser
            import threading
            def open_browser():
                time.sleep(1)
                webbrowser.open(main_url)
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Frontend server stopped by user")
        return True
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {PORT} is already in use")
            print(f"   Try accessing: http://{HOST}:{PORT}/src/index.html")
            print(f"   Or stop the existing server and try again")
        else:
            print(f"❌ Error starting server: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main function"""
    print("🔍 Checking if API server is running...")
    
    # Check if API server is running
    try:
        import requests
        response = requests.get("http://127.0.0.1:7777/hello/connectivity-test", timeout=3)
        if response.status_code == 200:
            print("✅ API server is running")
        else:
            print("⚠️ API server responded with unexpected status")
    except Exception:
        print("❌ API server is not running!")
        print("💡 Please start the API server first:")
        print("   python start_server.py")
        print()
        input("Press Enter after starting the API server...")
    
    # Start the frontend server
    start_frontend_server()

if __name__ == "__main__":
    main()
