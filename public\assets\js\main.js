const {
  app,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
} = require('electron')

const path = require('path')
const fs = require("fs")
const isDev = require("electron-is-dev")
const execFile = require("child_process").execFile

const API_PROD_PATH = path.join(process.resourcesPath, "../lib/api/api.exe")
const API_DEV_PATH = path.join(__dirname, "../../../engine/api.py")
const INDEX_PATH = path.join(__dirname, '../../src/index.html')
const app_instance = app.requestSingleInstanceLock()

// Global variable to store the Python process
let pythonProcess = null;

// Function to check if API server is running
async function checkServerConnectivity() {
  const axios = require('axios');
  const maxRetries = 10;
  const retryDelay = 1000; // 1 second

  for (let i = 0; i < maxRetries; i++) {
    try {
      console.log(`Checking server connectivity (attempt ${i + 1}/${maxRetries})...`);
      const response = await axios.get('http://127.0.0.1:7777/hello/connectivity-test', { timeout: 2000 });
      if (response.status === 200) {
        console.log('✅ API server is ready!');
        return true;
      }
    } catch (error) {
      console.log(`❌ Server not ready yet (attempt ${i + 1}/${maxRetries})`);
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  console.log('⚠️ Server connectivity check failed after all retries');
  return false;
}

// check if current app is Production or Development using electron-is-dev library
// current app is not production, just run the API from api.py,else run the api from API_PROD_PATH
if (isDev) {
  try {
    require('electron-reloader')(module)
  } catch (_) {}

  const {
    PythonShell
  } = require('python-shell')

  // Start Python API server using uvicorn
  try {
    console.log('Starting Python FastAPI server...');

    const options = {
      mode: 'text',
      pythonPath: 'python',
      pythonOptions: ['-u'], // unbuffered stdout
      scriptPath: path.join(__dirname, '../../../'),
      args: []
    };

    pythonProcess = new PythonShell('start_server.py', options);

    pythonProcess.on('message', function (message) {
      console.log('FastAPI Server:', message);

      // Check if server started successfully
      if (message.includes('Uvicorn running on') || message.includes('Application startup complete') ||
          message.includes('Server will be available at')) {
        console.log('✅ FastAPI server started successfully on http://127.0.0.1:7777');
      }
    });

    pythonProcess.on('error', function (error) {
      console.error('FastAPI Server Error:', error);
    });

    pythonProcess.on('close', function (code) {
      console.log(`FastAPI server closed with code: ${code}`);
    });

    console.log('FastAPI server startup initiated...');
  } catch (error) {
    console.error('Failed to start FastAPI server:', error);

    // Fallback: try to start using the start_server.py script
    try {
      console.log('Trying fallback method with start_server.py...');
      const fallbackOptions = {
        mode: 'text',
        pythonPath: 'python',
        scriptPath: path.join(__dirname, '../../../'),
        args: []
      };

      pythonProcess = new PythonShell('start_server.py', fallbackOptions);

      pythonProcess.on('message', function (message) {
        console.log('Start Server:', message);
      });

      pythonProcess.on('error', function (error) {
        console.error('Start Server Error:', error);
      });

    } catch (fallbackError) {
      console.error('Fallback method also failed:', fallbackError);
    }
  }
} else {
  pythonProcess = execFile(API_PROD_PATH, {
    windowsHide: true,
  });
}

//create Main Window
function createWindow() {
  console.log('Creating main window...');
  console.log('Index path:', INDEX_PATH);

  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false // Allow local API calls
    }
  })

  // Wait for server to be ready before loading the page
  if (isDev) {
    checkServerConnectivity().then((isReady) => {
      if (isReady) {
        // and load the index.html of the app.
        mainWindow.loadFile(INDEX_PATH).then(() => {
          console.log('Main window loaded successfully');
        }).catch((error) => {
          console.error('Failed to load main window:', error);
        });
      } else {
        console.log('Loading window anyway - server may start later');
        mainWindow.loadFile(INDEX_PATH).then(() => {
          console.log('Main window loaded (server not ready)');
        }).catch((error) => {
          console.error('Failed to load main window:', error);
        });
      }
    });
  } else {
    // In production, load immediately
    mainWindow.loadFile(INDEX_PATH).then(() => {
      console.log('Main window loaded successfully');
    }).catch((error) => {
      console.error('Failed to load main window:', error);
    });
  }

  // Open the DevTools.
  if (isDev) {
    mainWindow.webContents.openDevTools();
    console.log('DevTools opened');
  }

  // Prevent the window from closing immediately
  mainWindow.on('close', (event) => {
    console.log('Main window is closing...');
  });

  // only one instance exists
  // change to focus if window is minimized
  if (!app_instance) {
    app.quit()
  } else {
    app.on("second-instance", (event, commandline, workingDirectory) => {
      if (mainWindow) {
        if (mainWindow.isMinimized()) mainWindow.restore()
        mainWindow.focus()
      }
    })
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  createWindow()
  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// kill all child process before-quit
app.on("before-quit", function () {
  console.log('Cleaning up processes...');

  if (pythonProcess) {
    try {
      if (isDev) {
        pythonProcess.terminate();
        console.log('Python API server terminated');
      } else {
        pythonProcess.kill("SIGINT");
        console.log('Python API process killed');
      }
    } catch (error) {
      console.error('Error terminating Python process:', error);
    }
  }
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit()
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.