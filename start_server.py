#!/usr/bin/env python3
"""
Start the FastAPI server for SC Copilot KI
This script starts the API server on the correct host and port
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_dependencies():
    """Check if required Python packages are installed"""
    required_packages = ['fastapi', 'uvicorn', 'pandas', 'pywin32', 'openai']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {missing_packages}")
        print(f"📦 Install them with: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_files():
    """Check if required files exist"""
    required_files = [
        'engine/api.py',
        'email_info.csv',
        'data.csv'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        if 'data.csv' in missing_files:
            print("   📝 data.csv is required for critical materials detection")
        if 'email_info.csv' in missing_files:
            print("   📝 email_info.csv is required for stakeholder email configuration")
        return False
    
    return True

def start_fastapi_server():
    """Start the FastAPI server"""
    print("🚀 Starting FastAPI Server for SC Copilot KI")
    print("=" * 50)
    
    # Check prerequisites
    if not check_dependencies():
        return False
    
    if not check_files():
        return False
    
    # Change to the project root directory
    project_root = os.path.dirname(os.path.abspath(__file__))
    os.chdir(project_root)
    
    print(f"📁 Working directory: {os.getcwd()}")
    print(f"🌐 Server will be available at: http://127.0.0.1:7777")
    print(f"📚 API documentation will be at: http://127.0.0.1:7777/docs")
    print()
    
    try:
        # Start the server using uvicorn
        cmd = [
            sys.executable, '-m', 'uvicorn',
            'engine.api:app',
            '--host', '127.0.0.1',
            '--port', '7777',
            '--reload'
        ]
        
        print(f"🔄 Starting server with command: {' '.join(cmd)}")
        print("📝 Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Start the server
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting server: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_server_connectivity():
    """Test if the server is running"""
    try:
        response = requests.get("http://127.0.0.1:7777/hello/connectivity-test", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
            return True
        else:
            print(f"⚠️ Server responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {e}")
        return False

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        # Test mode - just check if server is running
        print("🔍 Testing server connectivity...")
        if test_server_connectivity():
            print("✅ Server is running correctly")
        else:
            print("❌ Server is not accessible")
            print("💡 Run 'python start_server.py' to start the server")
        return
    
    # Normal mode - start the server
    start_fastapi_server()

if __name__ == "__main__":
    main()
