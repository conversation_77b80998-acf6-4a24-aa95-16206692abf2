#!/usr/bin/env python3
"""
Environment setup script for SC Copilot KI
This script helps configure the required environment variables and files
"""

import os
import json
from datetime import datetime

def check_environment_variables():
    """Check if required environment variables are set"""
    required_vars = [
        'AZURE_OPENAI_ENDPOINT',
        'AZURE_OPENAI_API_KEY',
        'AZURE_OPENAI_DEPLOYMENT',
        'AZURE_OPENAI_API_VERSION'
    ]
    
    print("🔍 Checking Environment Variables...")
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Don't print the full API key for security
            if 'API_KEY' in var:
                print(f"   ✅ {var}: {'*' * 20}...{value[-4:]}")
            else:
                print(f"   ✅ {var}: {value}")
        else:
            print(f"   ❌ {var}: Not set")
            missing_vars.append(var)
    
    return missing_vars

def create_env_file_template():
    """Create a .env file template"""
    env_template = """# Azure OpenAI Configuration for SC Copilot KI
# Copy this file to .env and fill in your actual values

# Azure OpenAI Endpoint (e.g., https://your-resource.openai.azure.com/)
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here

# Azure OpenAI API Key
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here

# Azure OpenAI Deployment Name (the name of your deployed model)
AZURE_OPENAI_DEPLOYMENT=gpt-4

# Azure OpenAI API Version
AZURE_OPENAI_API_VERSION=2024-02-01

# Optional: Default email account for sending emails
DEFAULT_EMAIL_ACCOUNT=<EMAIL>
"""
    
    try:
        with open('.env.template', 'w') as f:
            f.write(env_template)
        print("✅ Created .env.template file")
        print("   📝 Copy this file to .env and fill in your Azure OpenAI credentials")
        return True
    except Exception as e:
        print(f"❌ Error creating .env.template: {str(e)}")
        return False

def check_required_files():
    """Check if required files exist and create them if missing"""
    required_files = {
        'email_info.csv': '''stakeholder,email_address,department
warehouse,<EMAIL>,Warehouse Management
supplier,<EMAIL>,Supply Chain
logistics,<EMAIL>,Logistics Department''',
        
        'incoming_emails.json': json.dumps({
            "timestamp": datetime.now().isoformat(),
            "total_emails": 0,
            "new_emails_detected": 0,
            "emails": []
        }, indent=2)
    }
    
    print("\n📁 Checking Required Files...")
    
    for filename, content in required_files.items():
        if os.path.exists(filename):
            print(f"   ✅ {filename}: Exists")
        else:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"   ✅ {filename}: Created")
            except Exception as e:
                print(f"   ❌ {filename}: Error creating - {str(e)}")

def check_data_csv():
    """Check if data.csv exists (required for critical materials detection)"""
    if os.path.exists('data.csv'):
        print("   ✅ data.csv: Exists")
        try:
            import pandas as pd
            df = pd.read_csv('data.csv')
            print(f"      📊 Contains {len(df)} material records")
            
            # Check required columns
            required_columns = ['Material', 'Stock', 'Status']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"      ⚠️  Missing columns: {missing_columns}")
            else:
                print("      ✅ All required columns present")
                
        except Exception as e:
            print(f"      ❌ Error reading data.csv: {str(e)}")
    else:
        print("   ❌ data.csv: Not found")
        print("      📝 This file is required for critical materials detection")
        print("      📝 It should contain columns: Material, Stock, Status, etc.")

def setup_python_environment():
    """Check Python dependencies"""
    print("\n🐍 Checking Python Dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'pandas',
        'pywin32',
        'openai',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}: Installed")
        except ImportError:
            print(f"   ❌ {package}: Not installed")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📦 To install missing packages, run:")
        print(f"   pip install {' '.join(missing_packages)}")

def main():
    """Main setup function"""
    print("🚀 SC Copilot KI Environment Setup")
    print("=" * 50)
    
    # Check environment variables
    missing_vars = check_environment_variables()
    
    # Create .env template if needed
    if missing_vars:
        print(f"\n⚠️  Missing environment variables: {missing_vars}")
        create_env_file_template()
    
    # Check required files
    check_required_files()
    
    # Check data.csv specifically
    check_data_csv()
    
    # Check Python dependencies
    setup_python_environment()
    
    # Summary and next steps
    print("\n" + "=" * 50)
    print("📋 Setup Summary")
    
    if missing_vars:
        print("❌ Environment variables need to be configured")
        print("   1. Copy .env.template to .env")
        print("   2. Fill in your Azure OpenAI credentials in .env")
        print("   3. Load environment variables (restart terminal or use python-dotenv)")
    else:
        print("✅ Environment variables are configured")
    
    print("\n📝 Next Steps:")
    print("1. Ensure Azure OpenAI credentials are properly configured")
    print("2. Make sure data.csv exists with material data")
    print("3. Run test_email_workflow.py to test the complete workflow")
    print("4. Start the FastAPI server: python -m uvicorn engine.api:app --host 127.0.0.1 --port 7777")
    print("5. Open the web interface and test the SC Copilot KI workflow")

if __name__ == "__main__":
    main()
